<?php

namespace App\Livewire;

use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class ReportSales extends Component
{
    public $sales_year_title;
    public $sales_year_value;
    public $sales_last_year_title;
    public $sales_last_year_value;

    public $total_transaction_year_title;
    public $total_transaction_year_value;
    public $total_transaction_last_year_title;
    public $total_transaction_last_year_value;

    public $sales_today;
    public $total_transaction_today;
    public $sales_yesterday;
    public $total_transaction_yesterday;

    public $growth_transaction_yearly_bg;
    public $growth_transaction_yearly_value;

    public $growth_transaction_daily_bg;
    public $growth_transaction_daily_value;

    public $growth_sales_yearly_bg;
    public $growth_sales_yearly_value;

    public $errorMessage;
    public $tab;
    protected $listeners = ['tab-changed' => 'handleTabChange'];

    public function render()
    {
        return view('livewire.report-sales');
    }

    public function mount($tab = null)
    {
        $this->tab = $tab ?? 'today';
        $this->fetchRecordSales($this->tab);
    }

    public function handleTabChange($event)
    {
        $this->tab = $event['tab'];
        $this->fetchRecordSales($event['tab']);
    }

    // fetch record from API
    public function fetchRecordSales($tab)
    {
        $this->tab = $tab;

        try {

            $curl = curl_init();

            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . session('authToken')
            ];

            // Add tab parameter to API request
            $url = env('API_URL') . '/report-sales?tab=' . $tab;
            
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => $headers,
            ));

            $response = curl_exec($curl);
            curl_close($curl);

            // HTTP status code
            $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            // Check status code
            if ($http_status == 200) {

                $result = json_decode($response); // Assume response in JSON format

                if ($result->status == "1") {

                    $this->sales_year_title = $result->sales_year->title;
                    $this->sales_year_value = $result->sales_year->value;
                    $this->sales_last_year_title = $result->sales_last_year->title;
                    $this->sales_last_year_value = $result->sales_last_year->value;

                    $this->total_transaction_year_title = $result->total_transaction_year->title;
                    $this->total_transaction_year_value = $result->total_transaction_year->value;
                    $this->total_transaction_last_year_title = $result->total_transaction_last_year->title;
                    $this->total_transaction_last_year_value = $result->total_transaction_last_year->value;

                    $this->sales_today = $result->sales_today;
                    $this->total_transaction_today = $result->total_transaction_today;
                    $this->sales_yesterday = $result->sales_yesterday;
                    $this->total_transaction_yesterday = $result->total_transaction_yesterday;

                    $this->growth_transaction_yearly_bg = $result->growth->transaction_yearly->background;
                    $this->growth_transaction_yearly_value = $result->growth->transaction_yearly->value;

                    $this->growth_transaction_daily_bg = $result->growth->transaction_daily->background;
                    $this->growth_transaction_daily_value = $result->growth->transaction_daily->value;
                } else {
                    throw new Exception('Unable to fetch record'); // supposed error coming from API
                }
            } else {
                Log::info('Unable to reach backend server ' . $http_status);
                throw new Exception('System is unable to retrieve the record. Please wait while retrying...'); // supposed error coming from API
            }
        } catch (Exception $e) {

            $this->errorMessage = $e->getMessage();
        }
    }

    function calculateGrowth($current, $last)
    {
        if ($last == 0) {
            if ($current > 0) {
                return "+100%";
            } else {
                return "0%";
            }
        }

        $growth = (($current - $last) / $last) * 100;

        // Format the growth percentage to include a + or - sign
        return ($growth >= 0 ? "+" : "") . round($growth, 2) . "%";
    }
}
