<?php

namespace App\Livewire;

use Exception;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\Attributes\Url;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\WithPagination;

class CompanySales extends Component
{
    use WithPagination;

    // URL tracked filters
    #[Url(history: true)]
    public $search = '';

    #[Url(history: true)]
    public $userType = '';

    #[Url(history: true)]
    public $minSales = '';

    #[Url(history: true)]
    public $maxSales = '';

    #[Url(history: true)]
    public $comPbt = '';

    #[Url(history: true)]
    public $return_page;

    public $sortBy = 'total_sales';
    public $sortDir = 'DESC';
    public $pbts = [];
    public $tab = '2025';

    // Temporary filter values
    public $filters = [
        'searchQuery' => '',
        'userTypeQuery' => '',
        'salesRange' => [
            'min' => '',
            'max' => ''
        ],
        'com_pbt' => ''
    ];

    // public $companies = [];
    public $errorMessage = '';
    public $isLoading = false;
    protected $listeners = ['tab-changed' => 'handleTabChange'];

    // User type options - you can modify these based on your needs
    public $userTypeOptions = [
        '' => 'All Types',
        'retail' => 'Retail',
        'wholesale' => 'Wholesale',
        'distributor' => 'Distributor'
    ];

    #[Url]
    public int $currentPage = 1;

    public function mount($tab = null)
    {
        // Initialize filter values from URL parameters
        $this->filters['searchQuery'] = $this->search;
        $this->filters['userTypeQuery'] = $this->userType;
        $this->filters['salesRange']['min'] = $this->minSales;
        $this->filters['salesRange']['max'] = $this->maxSales;
        $this->filters['com_pbt'] = $this->comPbt;

        $this->currentPage = request()->query('page', 1);
        $this->tab = 'today';
        $this->fetchRecord($this->tab);
    }

    public function applyFilters()
    {
        $this->userType = $this->filters['userTypeQuery'];
        $this->minSales = $this->filters['salesRange']['min'];
        $this->maxSales = $this->filters['salesRange']['max'];
        $this->comPbt = $this->filters['com_pbt'];

        $this->currentPage = 1; // Reset to first page when filtering
        $this->fetchRecord($this->tab);
    }

    public function resetFilters()
    {
        $this->reset('search', 'userType', 'minSales', 'maxSales', 'comPbt');
        $this->filters = [
            'searchQuery' => '',
            'userTypeQuery' => '',
            'salesRange' => [
                'min' => '',
                'max' => ''
            ],
            'com_pbt' => ''
        ];
        $this->tab = $tab ?? date('Y');
        $this->currentPage = 1;
        $this->fetchRecord($this->tab);
    }

    public function sortTable($sortBy)
    {
        $this->tab = $tab ?? date('Y');
        $this->sortBy = $sortBy;
        $this->sortDir = $this->sortDir === 'ASC' ? 'DESC' : 'ASC';
        $this->fetchRecord($this->tab);
    }

    public function fetchRecord($tab)
    {
        if ($tab && $tab == 'all') {
            $start_date = Carbon::createFromDate(2024, 1, 1)->startOfYear()->format('Y-m-d');
            $end_date = Carbon::now()->endOfDay()->format('Y-m-d');
        } else if ($tab && $tab == date('Y') - 1) { // last year
            $start_date = Carbon::createFromDate(date('Y') - 1, 1, 1)->startOfYear()->format('Y-m-d');
            $end_date = Carbon::createFromDate(date('Y') - 1, 1, 1)->endOfYear()->format('Y-m-d');
        } else if ($tab && $tab == date('Y')) {  // this year
            $start_date = Carbon::createFromDate(date('Y'), 1, 1)->startOfYear()->format('Y-m-d');
            $end_date = Carbon::createFromDate(date('Y'), 1, 1)->endOfYear()->format('Y-m-d');
        } else if ($tab && $tab == 'today') {
            $start_date = Carbon::now()->startOfDay()->format('Y-m-d');
            $end_date = Carbon::now()->endOfDay()->format('Y-m-d');
        }

        try {
            $this->isLoading = true;
            $this->errorMessage = '';

            $query = User::select([
                'users.id as user_id',
                'companies.com_name',
                'companies.pbt_id',
                'companies.com_address',
                'pbts.name as pbt_name',
                'pbts.code as pbt_code',
                'categories.name as category',
                'user_details.first_name',
                'users.latitude',
                'users.longitude',
                DB::raw('COALESCE(SUM(orders.grandtotal_decimal), 0) as total_sales')
            ])
                ->leftJoin('companies', 'companies.user_id', '=', 'users.id')
                ->leftJoin('categories', 'categories.id', '=', 'companies.category_id')
                ->leftJoin('orders', 'orders.company_id', '=', 'companies.id')
                ->leftJoin('pbts', 'pbts.id', '=', 'companies.pbt_id')
                ->leftJoin('user_details', 'user_details.user_id', '=', 'users.id')
                ->whereNull('orders.deleted_at')
                ->whereBetween('orders.order_date', [$start_date, $end_date]) // Already has date filtering

                // Filters
                ->when($this->search, function ($q) {
                    $q->where(function ($query) {
                        $query->where('companies.com_name', 'like', "%{$this->search}%")
                            ->orWhere('categories.name', 'like', "%{$this->search}%")
                            ->orWhere('user_details.first_name', 'like', "%{$this->search}%");
                    });
                })
                ->when($this->userType, function ($q) {
                    $q->where('users.access_module', $this->userType);
                })
                ->when($this->minSales && $this->maxSales, function ($q) {
                    $q->havingRaw('total_sales >= ? AND total_sales <= ?', [$this->minSales, $this->maxSales]);
                })
                ->when($this->minSales, function ($q) {
                    $q->having('total_sales', '>=', $this->minSales);
                })
                ->when($this->maxSales, function ($q) {
                    $q->having('total_sales', '<=', $this->maxSales);
                })
                ->when($this->comPbt, function ($q) {
                    $q->where('companies.pbt_id', $this->comPbt);
                })

                // Grouping and sorting
                ->groupBy(
                    'users.id',
                    'companies.com_name',
                    'companies.pbt_id',
                    'companies.com_address',
                    'pbts.name',
                    'pbts.code',
                    'categories.name',
                    'user_details.first_name',
                    'users.latitude',
                    'users.longitude'
                )
                ->orderBy($this->sortBy, $this->sortDir);

            // Return the paginated results
            return $query->paginate(10); // 10 items per page

        } catch (\Exception $e) {
            Log::error('DB Error: ' . $e->getMessage());
            $this->errorMessage = 'Failed to load data';
            return collect(); // Return an empty collection in case of error
        } finally {
            $this->isLoading = false;
        }
    }

    public function validateSalesRange()
    {
        // Convert to numeric for comparison
        $min = is_numeric($this->filters['salesRange']['min']) ? floatval($this->filters['salesRange']['min']) : null;
        $max = is_numeric($this->filters['salesRange']['max']) ? floatval($this->filters['salesRange']['max']) : null;

        if ($min !== null && $max !== null && $min > $max) {
            $this->errorMessage = 'Jualan minima tidak boleh melebihi jualan maksima';
            return false;
        }

        return true;
    }

    public function handleTabChange($tab)
    {
        $this->tab = $tab;
        $this->fetchRecord($this->tab);
    }

    public function render()
    {
        $companies = $this->fetchRecord($this->tab);
        return view('livewire.company-sales', [
            'companies' => $companies,
        ]);
    }
}
