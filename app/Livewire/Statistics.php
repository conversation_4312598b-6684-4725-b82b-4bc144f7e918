<?php

namespace App\Livewire;

use Exception;
use Carbon\Carbon;
use Livewire\Component;
use Illuminate\Support\Facades\Log;
use Livewire\WithPagination;

class Statistics extends Component
{
    use WithPagination;

    #[Url(history: true)]
    public $chartData = [];

    // public $startDate;
    // public $endDate;
    public $errorMessage;
    // public $sales;
    // public $total_transaction;
    public $total_transaction_user;
    public $currentPage = 1;
    // public $user_data;
    // public $pagination;
    // public $filterType = 'today';
    public $start;
    public $end;
    public $sortBy = 'total_sales';
    public $sortDir = 'DESC';
    // public $payment_method;
    protected $listeners = ['tab-changed' => 'handleTabChange'];
    public $filterType = 'today'; // Default filter type
    public $startDate;
    public $endDate;
    // public $user_data = []; // Initialize as an empty array
    public $pagination = []; // Initialize as an empty array
    public $sales = '';
    public $total_transaction = '';
    public $payment_method = []; // Initialize as an empty array
    public function mount()
    {
        $now = Carbon::now();

        // if ($request->has('startDate')) {
        //     $this->startDate = $request->input('startDate');
        //     $this->endDate = $request->input('endDate');
        // } else {
        //     $this->startDate = $this->endDate = $now->toDateString();
        // }

        // if ($request->has('filterData')) {
        //     $this->filterType = $request->input('filterData') ?? 'today';
        // }

        // if ($request->has('return_page')) {
        //     $this->currentPage = $request->input('return_page') ?? 1;
        // }

        // if ($request->has('sortBy')) {
        //     $this->sortBy = $request->input('sortBy') ?? 'total_sales';
        // }
        // $this->filterType = $request->input('filterData') ?? 'today';
        $this->filterType = 'today';

        $this->statistics($this->filterType);
    }

    public function statistics($filterType)
    {
        try {
            $now = Carbon::now();

            // Set the date range based on the filter type
            if ($filterType === 'today') {
                $this->startDate = $this->endDate = $now->toDateString();
            } elseif ($filterType === 'yesterday') {
                $yesterday = $now->copy()->subDay();
                $this->startDate = $this->endDate = $yesterday->toDateString();
            } elseif ($filterType === 'this_week') {
                $this->startDate = $now->copy()->startOfWeek()->toDateString();
                $this->endDate = $now->copy()->endOfWeek()->toDateString();
            } elseif ($filterType === 'this_month') {
                $this->startDate = $now->copy()->startOfMonth()->toDateString();
                $this->endDate = $now->copy()->endOfMonth()->toDateString();
            } elseif ($filterType === 'date_range') {
                // Use the already set startDate and endDate
            } else {
                $this->startDate = $this->endDate = $now->toDateString();
            }

            // Temporary fallback with mock data until API endpoints are ready
            Log::info('Statistics: Using fallback data due to API migration');

            $this->sales = "RM 0.00";
            $this->total_transaction = "0";

            // Create mock data for testing
            $mockData = collect([
                (object)[
                    'user_id' => 1,
                    'first_name' => 'Test User 1',
                    'com_name' => 'Test Company 1',
                    'pbt_code' => 'PBT001',
                    'total_sales' => 0,
                    'total_trx' => 0
                ],
                (object)[
                    'user_id' => 2,
                    'first_name' => 'Test User 2',
                    'com_name' => 'Test Company 2',
                    'pbt_code' => 'PBT002',
                    'total_sales' => 0,
                    'total_trx' => 0
                ]
            ]);

            // Create a proper paginated collection
            $pagination = new \Illuminate\Pagination\LengthAwarePaginator(
                $mockData,
                $mockData->count(),
                10,
                1,
                [
                    'path' => request()->url(),
                    'pageName' => 'page'
                ]
            );

            // Initialize empty payment method data
            $this->payment_method = [];

            return $pagination;

        } catch (Exception $e) {
            Log::error('MBI Statistics Error: ' . $e->getMessage());
            $this->errorMessage = 'Data temporarily unavailable during system migration';

            // Return an empty paginated collection in case of an error
            return new \Illuminate\Pagination\LengthAwarePaginator(
                collect([]),
                0,
                10,
                1,
                [
                    'path' => request()->url(),
                    'pageName' => 'page'
                ]
            );
        }
    }

    public function filterData($filterType, $startDate = null, $endDate = null)
    {
        $now = Carbon::now();

        if ($filterType === 'today') {
            $this->startDate = $this->endDate = $now->toDateString();
        } elseif ($filterType === 'yesterday') {
            $yesterday = $now->subDay();
            $this->startDate = $this->endDate = $yesterday->toDateString();
        } elseif ($filterType === 'this_week') {
            $this->startDate = $now->copy()->startOfWeek()->toDateString();
            $this->endDate = $now->copy()->endOfWeek()->toDateString();
        } elseif ($filterType === 'this_month') {
            $this->startDate = $now->copy()->startOfMonth()->toDateString();
            $this->endDate = $now->copy()->endOfMonth()->toDateString();
        } elseif ($filterType === 'date_range') {
            $this->startDate = $startDate;
            $this->endDate = $endDate;
        } else {
            $this->startDate = $this->endDate = $now->toDateString();
        }

        $this->filterType = $filterType;
        $this->statistics($filterType);
    }

    public function render()
    {
        $user_data = $this->statistics($this->filterType);

        return view('livewire.statistics', [
            'user_data' => $user_data,
            'sales' => $this->sales,
            'total_transaction' => $this->total_transaction,
            'filterType' => $this->filterType,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ]);
    }

    public function sortTable($sortBy)
    {
        $this->filterType = $this->filterType ?? 'today';
        $this->sortBy = $sortBy;
        $this->sortDir = $this->sortDir === 'ASC' ? 'DESC' : 'ASC';
        $this->statistics($this->filterType);
    }

    // public function fetchStatisticsData()
    // {
    //     try {

    //         $curl = curl_init();

    //         $headers = [
    //             'Content-Type: application/json',
    //             'Authorization: Bearer ' . session('authToken')
    //         ];

    //         $queryParams = http_build_query([
    //             'startDate' => $this->startDate,
    //             'endDate' => $this->endDate,
    //             'page' => $this->currentPage,
    //             'sortBy' => $this->sortBy,
    //             'sortDir' => $this->sortDir
    //         ]);

    //         curl_setopt_array($curl, array(
    //             CURLOPT_URL => env('API_URL') . '/statistics/index' . '?' . $queryParams,
    //             CURLOPT_RETURNTRANSFER => true,
    //             CURLOPT_ENCODING => '',
    //             CURLOPT_MAXREDIRS => 10,
    //             CURLOPT_TIMEOUT => 0,
    //             CURLOPT_FOLLOWLOCATION => true,
    //             CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    //             CURLOPT_CUSTOMREQUEST => 'GET',
    //             CURLOPT_HTTPHEADER => $headers,
    //         ));

    //         $response = curl_exec($curl);
    //         curl_close($curl);
    //         // dd($response);
    //         // HTTP status code
    //         $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    //         // Check status code
    //         if ($http_status == 200) {

    //             $result = json_decode($response); // Assume response in JSON format

    //             if ($result->status == "1") {

    //                 $this->sales = $result->sales;
    //                 $this->total_transaction = $result->total_transaction;
    //                 $this->currentPage = $result->users->current_page;
    //                 $this->user_data = $result->users->data;
    //                 $this->pagination = $result->users;
    //                 $this->filterType = $this->filterType;

    //                 if (!empty($result->payment_method)) {
    //                     $payment_method = self::groupingPaymentChannel($result->payment_method);

    //                     $this->chartData['table'] = $payment_method;
    //                     $this->chartData['chart'] = self::generateChart($payment_method);
    //                 }
    //             } else {

    //                 throw new Exception("Error Processing Request");
    //             }
    //         } else {

    //             throw new Exception("System is unable to retrieve the record. Please wait while retrying...");
    //         }
    //     } catch (Exception $e) {

    //         $this->errorMessage = $e->getMessage();
    //     }
    // }

    // prepare chart data
    private static function generateChart($data)
    {

        $arr = [
            'labels' => [],
            'series' => [],
            'amount' => [],
        ];


        foreach ($data as $key => $method) {

            $arr['labels'][] = $key;
            $arr['series'][] = $method['TOTALTXN'];
            $arr['amount'][] = $method['AMOUNTXN'];
        }

        return $arr;
    }

    // grouping payment channels @ statistics page
    private static function groupingPaymentChannel($data)
    {

        if (!empty($data)) {

            // Initializing result array
            $channels = [];

            // Grouping definitions
            $groupings = [
                'CREDITCARD' => ['DEBITCARD', 'CREDITCARD', 'TAP2PHONE', 'Credit/Debit(NFC)'],
                'BANKTRANSFER' => ['BANKTRANSFER', 'OTHERS'],
                'QRPAY' => ['QR_GKASH', 'QRPAY', 'ScanQRGkash'],
                'EWALLET' => ['TOUCHNGO', 'EWALLET']
            ];

            // Loop through each item in the input data
            foreach ($data as $item) {
                $name = $item->name;
                $quantity = $item->quantity;
                $amount = $item->amount;

                // Check if the item belongs to a group
                $groupFound = false;
                foreach ($groupings as $group => $subItems) {
                    if (in_array($name, $subItems)) {
                        $groupFound = true;

                        // If the group doesn't exist in the result, create it
                        if (!isset($channels[$group])) {
                            $channels[$group] = [
                                'TOTALTXN' => 0,
                                'AMOUNTXN' => 0,
                                'SUBCHANNEL' => []
                            ];
                        }

                        // Update the total quantity and amount
                        $channels[$group]['TOTALTXN'] += $quantity;
                        $channels[$group]['AMOUNTXN'] += $amount;
                        // Add the item to the SUBCHANNEL
                        $channels[$group]['SUBCHANNEL'][$name] = [
                            'TOTALTXN' => $quantity,
                            'AMOUNTXN' => $amount
                        ];
                        break; // Break as we've found the group
                    }
                }

                // If the item does not belong to any group, add it directly
                if (!$groupFound) {
                    $channels[$name] = [
                        'TOTALTXN' => $quantity,
                        'AMOUNTXN' => $amount
                    ];
                }
            }

            return $channels;
        } else {

            throw new Exception("Payment channel result is empty");
        }
    }

}
