<?php

namespace App\Livewire;

use App\Models\Company;
use App\Models\Order;
use App\Models\PBT;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class StatisticsActive extends Component
{
    public $pbtTotal = 0;
    public $pbtActive = 0;
    public $merchantTotal = 0;
    public $merchantActive = 0;
    public $newTotal = 0;
    public $newCategory = 0;
    public $totalCustomer = 0;
    public $heat_maps = [];
    public $year;
    public $errorMessage;
    protected $listeners = ['tab-changed' => 'handleTabChange'];
    public $tab;

    public function mount($tab = null) 
    {
        // $this->tab = $tab ?? date('Y'); // Default to current year if no tab is provided
        $this->tab = 'today';
        $this->fetchStatisticsActiveData($this->tab); // Pass $this->tab to avoid the error
    }

    public function handleTabChange($tab)
    {
        $this->tab = $tab;
        $this->fetchStatisticsActiveData($tab);
    }

    public function fetchStatisticsActiveData($tab)
    {
        $userIds = User::where('access_module', 'like', '%plats%')
            ->where('users.username', 'not like', 'uplats012')
            ->pluck('id');
        $companies = Company::select([
            'id',
            'com_state',
            'category_id',
            'created_at',
        ])
            ->whereIn('user_id', $userIds)
            ->get();

        $pbts = PBT::select([
            'id',
            'name',
            'code',
            'state'
        ])
            ->where('state', 'Selangor') // limit the list to Selangor only
            ->get();

        $start_date = Carbon::now()->startOfDay()->format('Y-m-d');
        $end_date = Carbon::now()->endOfDay()->format('Y-m-d');

        if($tab && $tab == 'all'){
            $start_date = Carbon::createFromDate(2024, 1, 1)->startOfYear()->format('Y-m-d');
            $end_date = Carbon::now()->endOfDay()->format('Y-m-d');
        } else if ($tab && $tab == date('Y') - 1){ // last year
            $start_date = Carbon::createFromDate(date('Y') - 1, 1, 1)->startOfYear()->format('Y-m-d');
            $end_date = Carbon::createFromDate(date('Y') -1, 1, 1)->endOfYear()->format('Y-m-d');
        } else if ($tab && $tab == date('Y')){  // this year
            $start_date = Carbon::createFromDate(date('Y'), 1, 1)->startOfYear()->format('Y-m-d');
            $end_date = Carbon::createFromDate(date('Y'), 1, 1)->endOfYear()->format('Y-m-d');
        } else if ($tab && $tab == 'today'){
            $start_date = Carbon::now()->startOfDay()->format('Y-m-d');
            $end_date = Carbon::now()->endOfDay()->format('Y-m-d');
        }

        // Base query for fetching orders with company and PBT details
        $baseQuery = Order::leftJoin('companies', 'companies.id', '=', 'orders.company_id')
            ->leftJoin('pbts', 'pbts.id', '=', 'companies.pbt_id')
            ->select([
                'orders.id',
                'companies.com_name as company_name',
                'companies.id as company_id',
                'companies.com_state',
                'orders.customer_name',
                'orders.order_date',
                'orders.order_latitude',
                'orders.order_longitude',
                'pbts.name as pbt_name',
                'pbts.code as pbt_code',
                'pbts.status as pbt_status',
                'pbts.state as pbt_state'
            ])
            ->whereIn('companies.id', $companies->pluck('id'))
        ->whereBetween('orders.order_date', [$start_date . " 00:00:00", $end_date . " 23:59:59"]);
        // Fetch all orders with company and PBT details
        $orders = $baseQuery->get();

        // Fetch active PBTs specifically from Selangor state
        $activePbts = $baseQuery->where('pbts.state', 'Selangor')
            ->where('pbts.status', 'active') // If you want to fetch active PBTs only
            ->get();

        $heatMapData = Order::select([
            DB::raw('ROUND(order_latitude, 3) as lat'),  // ~100m precision
            DB::raw('ROUND(order_longitude, 3) as lng'),
            DB::raw('COUNT(*) as count')
        ])
            ->whereNotNull('order_latitude')
            ->whereNotNull('order_longitude')
            ->whereBetween('order_date', [$start_date, $end_date])
            ->groupBy(DB::raw('ROUND(order_latitude, 3), ROUND(order_longitude, 3)'))
            ->orderByDesc('count')
            ->limit(1000)  // Limit to top 1000 points
            ->get();

        try {
            $heatMaps = Cache::remember("heatmap_{$start_date}_{$end_date}", 300, function() use ($start_date, $end_date) {
                $heatMapData = Order::select([
                        DB::raw('ROUND(order_latitude, 3) as lat'),
                        DB::raw('ROUND(order_longitude, 3) as lng'),
                        DB::raw('COUNT(*) as count')
                    ])
                    ->whereNotNull('order_latitude')
                    ->whereNotNull('order_longitude')
                    ->whereBetween('order_date', [$start_date, $end_date])
                    ->groupBy(DB::raw('ROUND(order_latitude, 3), ROUND(order_longitude, 3)'))
                    ->orderByDesc('count')
                    ->limit(1000)
                    ->get();

                return $heatMapData->map(function($point) {
                    return [
                        'lat' => (float)$point->lat,
                        'lng' => (float)$point->lng,
                        'count' => $point->count
                    ];
                })->toArray();
            });
        } catch (\Exception $e) {
            Log::error('Error on heatmap : ' . $e->getMessage());
            return response()->json([
                'status' => '0',
                'message' => $e->getMessage()
            ]);
        }


        $totalMerchant = $companies->count();

        //Get new companies registered this month
        $newCompanies = $companies->where('created_at', '>=', Carbon::now()->startOfMonth()->format('Y-m-d H:i:s'));
        $getCategoryNewCompanies = $newCompanies->groupBy('category_id')->count();


        $this->pbtTotal = $pbts->count();
        $this->pbtActive = $activePbts->groupBy('pbt_code')->count();

        $this->merchantTotal = $totalMerchant;
        $this->merchantActive = $baseQuery->distinct('companies.user_id')->count();

        $this->heat_maps = $heatMaps;

    }

    public function render()
    {
        return view('livewire.statistics-active');
    }
}
