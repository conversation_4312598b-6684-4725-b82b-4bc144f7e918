<?php

namespace App\View\Components\merchant;

// use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class BusinessInfo extends Component
{
        public $user_name;
        public $company_name;
        public $company_address;
        public $company_mobile;
        public $company_email;
        public $company_status;
        public $company_register_date;
        public $company_pbt;

        public function __construct(
    $user_name = null,
    $company_name = null,
    $company_address = null,
    $company_mobile = null,
    $company_email = null,
    $company_status = null,
    $company_pbt = null,
    $company_register_date = null
)
{
    $this->user_name = $user_name;
    $this->company_name = $company_name;
    $this->company_address = $company_address;
    $this->company_mobile = $company_mobile;
    $this->company_email = $company_email;
    $this->company_status = $company_status;
    $this->company_pbt = $company_pbt;
    $this->company_register_date = $company_register_date;
}

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('components.merchant.business-info');
    }
}
